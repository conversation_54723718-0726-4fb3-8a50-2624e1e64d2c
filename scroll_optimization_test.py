#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
滚动优化测试脚本
用于验证新的滚动优化功能是否正常工作
"""

import tkinter as tk
import time
import math

class ScrollOptimizationTest:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("滚动优化测试")
        self.root.geometry("800x600")
        
        # 创建测试界面
        self.create_test_interface()
        
        # 滚动相关变量
        self._animation_active = False
        self._sync_pending = False
        self._last_frame_time = 0
        self._easing_cache = {}
        self._scroll_animation_id = None
        
    def create_test_interface(self):
        """创建测试界面"""
        # 控制面板
        control_frame = tk.Frame(self.root)
        control_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # 滚动速度选择
        tk.Label(control_frame, text="滚动速度:").pack(side=tk.LEFT)
        
        self.speed_var = tk.StringVar(value="2.0")
        speeds = [("瞬间", "0.0"), ("极快", "0.5"), ("快速", "1.0"), 
                 ("正常", "2.0"), ("慢速", "4.0"), ("极慢", "8.0"), ("超慢", "15.0")]
        
        for name, speed in speeds:
            tk.Radiobutton(control_frame, text=name, variable=self.speed_var, 
                          value=speed).pack(side=tk.LEFT)
        
        # 测试按钮
        button_frame = tk.Frame(self.root)
        button_frame.pack(fill=tk.X, padx=10, pady=5)
        
        tk.Button(button_frame, text="测试滚动到顶部", 
                 command=lambda: self.test_scroll(0.0)).pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="测试滚动到中间", 
                 command=lambda: self.test_scroll(0.5)).pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="测试滚动到底部", 
                 command=lambda: self.test_scroll(1.0)).pack(side=tk.LEFT, padx=5)
        
        # 创建滚动区域
        self.create_scroll_area()
        
    def create_scroll_area(self):
        """创建可滚动区域"""
        # 创建滚动条
        self.scrollbar = tk.Scrollbar(self.root, orient=tk.VERTICAL)
        self.scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 创建画布
        self.canvas = tk.Canvas(self.root, bg='lightgray', 
                               yscrollcommand=self.scrollbar.set)
        self.canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # 设置滚动条命令
        self.scrollbar.config(command=self.canvas.yview)
        
        # 创建内容框架
        self.content_frame = tk.Frame(self.canvas)
        self.canvas_window = self.canvas.create_window(0, 0, anchor="nw", 
                                                      window=self.content_frame)
        
        # 添加测试内容
        self.add_test_content()
        
        # 绑定事件
        self.content_frame.bind('<Configure>', self.on_frame_configure)
        self.canvas.bind('<Configure>', self.on_canvas_configure)
        self.canvas.bind('<MouseWheel>', self.on_mousewheel)
        
    def add_test_content(self):
        """添加测试内容"""
        colors = ['red', 'green', 'blue', 'yellow', 'orange', 'purple', 'pink', 'cyan']
        
        for i in range(100):
            color = colors[i % len(colors)]
            label = tk.Label(self.content_frame, 
                           text=f"测试行 {i+1} - 这是用于测试滚动优化的内容",
                           bg=color, fg='white' if color in ['red', 'green', 'blue', 'purple'] else 'black',
                           height=2, font=('Arial', 12))
            label.pack(fill=tk.X, padx=5, pady=2)
    
    def on_frame_configure(self, event):
        """当内容框架大小改变时更新滚动区域"""
        self.canvas.configure(scrollregion=self.canvas.bbox("all"))
    
    def on_canvas_configure(self, event):
        """当画布大小改变时调整内容框架宽度"""
        canvas_width = event.width
        self.canvas.itemconfig(self.canvas_window, width=canvas_width)
    
    def on_mousewheel(self, event):
        """鼠标滚轮事件"""
        self.canvas.yview_scroll(int(-1 * (event.delta / 120)), "units")
    
    def test_scroll(self, target_position):
        """测试滚动到指定位置"""
        duration = float(self.speed_var.get())
        print(f"开始测试滚动，目标位置: {target_position}, 持续时间: {duration}秒")
        
        if duration == 0.0:
            # 瞬间滚动
            self.canvas.yview_moveto(target_position)
            print("瞬间滚动完成")
        else:
            # 平滑滚动
            self.smooth_scroll_to(target_position, duration)
    
    def smooth_scroll_to(self, target_position, duration):
        """优化的平滑滚动实现"""
        # 获取当前位置
        current_position = self.canvas.yview()[0]
        
        # 如果位置相同，无需滚动
        if abs(current_position - target_position) < 0.001:
            print("位置相同，无需滚动")
            return
        
        # 记录初始状态
        start_position = current_position
        distance = target_position - current_position
        start_time = time.time()
        
        # 取消之前可能正在进行的滚动动画
        if hasattr(self, '_scroll_animation_id') and self._scroll_animation_id:
            self.root.after_cancel(self._scroll_animation_id)
        
        # 初始化动画状态
        self._animation_active = True
        self._last_frame_time = start_time
        
        print(f"开始平滑滚动: {start_position:.3f} -> {target_position:.3f}, 距离: {distance:.3f}")
        
        # 执行高性能平滑滚动动画
        def animation_frame():
            if not self._animation_active:
                return
                
            # 使用高精度时间计算
            current_time = time.time()
            elapsed = current_time - start_time
            
            # 检查是否完成动画
            if elapsed >= duration:
                # 动画结束，确保精确到达目标位置
                self._animation_active = False
                self.canvas.yview_moveto(target_position)
                print(f"滚动动画完成，最终位置: {target_position:.3f}")
                return
            
            # 计算进度比例(0-1)
            progress = min(elapsed / duration, 1.0)
            
            # 使用优化的缓动函数
            ease = self._advanced_easing(progress)
            
            # 计算新位置
            new_position = start_position + distance * ease
            
            # 应用新位置
            self.canvas.yview_moveto(new_position)
            
            # 自适应帧率控制
            frame_interval = self._calculate_optimal_frame_interval(current_time)
            self._scroll_animation_id = self.root.after(frame_interval, animation_frame)
        
        # 开始动画
        animation_frame()
    
    def _advanced_easing(self, t):
        """高级缓动函数，提供更自然的动画效果"""
        if t <= 0:
            return 0
        if t >= 1:
            return 1
            
        # 使用四次贝塞尔曲线实现更平滑的缓动
        return t * t * (3.0 - 2.0 * t) * (1.0 + 0.5 * t * (1.0 - t))
    
    def _calculate_optimal_frame_interval(self, current_time):
        """计算最优帧间隔，自适应性能"""
        # 计算上一帧的处理时间
        frame_time = current_time - self._last_frame_time
        self._last_frame_time = current_time
        
        # 基于性能自适应调整帧率
        if frame_time > 0.020:  # 如果帧时间超过20ms
            return 20  # 降低到50fps
        elif frame_time > 0.016:  # 如果帧时间超过16ms
            return 16  # 保持60fps
        else:
            return 12  # 提升到83fps（更流畅）
    
    def run(self):
        """运行测试"""
        print("滚动优化测试启动")
        print("使用不同的滚动速度测试滚动效果")
        print("观察是否有闪烁现象，以及滚动是否丝滑")
        self.root.mainloop()

if __name__ == "__main__":
    test = ScrollOptimizationTest()
    test.run()
